import{a as B}from"./chunk-EXGJLNXY.js";import{a as l}from"./chunk-WPPT3EJF.js";import"./chunk-2GT6F2KJ.js";import"./chunk-2LL5MXLB.js";import{B as m,C as p,Db as j,F as M,G as u,Hb as N,Ib as T,K as i,L as o,M as s,Q as d,R as v,Wb as U,Xb as F,Y as c,Yb as q,_ as O,a as h,da as P,ec as D,f as b,fc as A,i as x,k as _,m as C,na as w,pa as k,qa as y,ra as f,ub as E,vb as I,wb as H,xb as $,ya as S,yb as z}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{g}from"./chunk-2R6CW7ES.js";var R=(()=>{class a{constructor(e){this.http=e,this.unreadCountSubject=new h(0),this.unreadCount$=this.unreadCountSubject.asObservable()}getHeaders(){let e=localStorage.getItem("auth_token");return new y({Authorization:`Bearer ${e}`,"Content-Type":"application/json"})}getNotifications(e=1,r=20,n){let t={page:e,per_page:r};return n&&(t.type=n),this.http.get(`${l.apiUrl}/notifications`,{headers:this.getHeaders(),params:t})}getUnreadCount(){return this.http.get(`${l.apiUrl}/notifications/unread-count`,{headers:this.getHeaders()})}markAsRead(e){return this.http.put(`${l.apiUrl}/notifications/${e}/read`,{},{headers:this.getHeaders()})}markAllAsRead(){return this.http.put(`${l.apiUrl}/notifications/mark-all-read`,{},{headers:this.getHeaders()})}deleteNotification(e){return this.http.delete(`${l.apiUrl}/notifications/${e}`,{headers:this.getHeaders()})}addReaction(e){return this.http.post(`${l.apiUrl}/notifications/${e}/reaction`,{},{headers:this.getHeaders()})}getStats(){return this.http.get(`${l.apiUrl}/notifications/stats`,{headers:this.getHeaders()})}updateUnreadCount(e){this.unreadCountSubject.next(e)}refreshUnreadCount(){return g(this,null,function*(){try{let e=yield this.getUnreadCount().toPromise();e&&this.updateUnreadCount(e.unread_count)}catch(e){console.error("Error refreshing unread count:",e)}})}getNotificationIcon(e){switch(e.type){case"evacuation_center_added":return"assets/evacuation-center-icon.png";case"emergency_alert":return"assets/emergency-icon.png";case"system_update":return"assets/system-icon.png";default:return"assets/alerto_icon.png"}}getNotificationColor(e){switch(e.type){case"evacuation_center_added":return"#42b883";case"emergency_alert":return"#e41e3f";case"system_update":return"#1877f2";default:return"#03b2dd"}}getTimeAgo(e){let r=new Date(e),t=Math.floor((new Date().getTime()-r.getTime())/1e3);return t<60?`${t}s`:t<3600?`${Math.floor(t/60)}m`:t<86400?`${Math.floor(t/3600)}h`:t<604800?`${Math.floor(t/86400)}d`:`${Math.floor(t/604800)}w`}static{this.\u0275fac=function(r){return new(r||a)(_(f))}}static{this.\u0275prov=x({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})();function K(a,Q){if(a&1&&(i(0,"ion-badge",25),c(1),o()),a&2){let e=v();m(),O(" ",e.unreadNotificationCount>99?"99+":e.unreadNotificationCount," ")}}var se=(()=>{class a{constructor(e,r,n,t,L){this.router=e,this.toastCtrl=r,this.http=n,this.notificationService=t,this.emergencyOverlay=L,this.unreadNotificationCount=0,this.notificationSubscription=null,this.pollSubscription=null,window.testEmergency=this.emergencyOverlay}ngOnInit(){this.notificationSubscription=this.notificationService.unreadCount$.subscribe(e=>{this.unreadNotificationCount=e}),this.loadUnreadCount(),this.pollSubscription=b(3e4).subscribe(()=>{this.notificationService.refreshUnreadCount()})}ngOnDestroy(){this.notificationSubscription&&this.notificationSubscription.unsubscribe(),this.pollSubscription&&this.pollSubscription.unsubscribe()}openDisasterMap(e){console.log(`\u{1F3E0} HOME: Opening disaster-specific map for: ${e}`);let r=e,n="";e==="earthquake"?(r="Earthquake",n="/earthquake-map"):e==="typhoon"?(r="Typhoon",n="/typhoon-map"):e==="flashflood"?(r="Flash Flood",n="/flood-map"):e==="fire"?(r="Fire",n="/fire-map"):e==="landslide"&&(r="Landslide",n="/landslide-map"),console.log(`\u{1F3E0} HOME: Navigating to ${n} for ${r}`),this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Opening ${r} evacuation centers...`,duration:2e3,color:"primary"}).then(t=>t.present()),this.router.navigate([n])}viewMap(){console.log("\u{1F3E0} HOME: Opening complete evacuation centers map"),this.toastCtrl.create({message:"\u{1F5FA}\uFE0F Opening complete evacuation centers map...",duration:2e3,color:"secondary"}).then(e=>e.present()),this.router.navigate(["/all-maps"])}loadUnreadCount(){return g(this,null,function*(){try{yield this.notificationService.refreshUnreadCount(),this.notificationService.unreadCount$.subscribe(e=>{this.unreadNotificationCount=e})}catch(e){console.error("Error loading unread notification count:",e)}})}openNotifications(){console.log("Notification button clicked! Navigating to notifications..."),this.router.navigate(["/notifications"])}static{this.\u0275fac=function(r){return new(r||a)(p(S),p(D),p(f),p(R),p(B))}}static{this.\u0275cmp=C({type:a,selectors:[["app-home"]],standalone:!0,features:[P],decls:55,vars:2,consts:[[3,"translucent"],["slot","end"],[1,"notification-button",3,"click"],["name","notifications-outline"],["class","notification-badge",4,"ngIf"],[1,"home-content"],[1,"disaster-container"],[1,"welcome-section"],[1,"welcome-title"],[1,"welcome-subtitle"],[1,"disaster-grid"],[1,"disaster","earthquake",3,"click"],[1,"card-icon-wrapper"],["src","assets/earthquake-icon.svg","alt","Earthquake"],[1,"card-indicator"],[1,"disaster","typhoon",3,"click"],["src","assets/icon/bagyo.png","alt","Typhoon"],[1,"disaster","flood",3,"click"],["src","assets/icon/baha.jpg","alt","Flood"],[1,"disaster","fire",3,"click"],["src","assets/icon/fire.jpg","alt","Fire"],[1,"disaster","landslide",3,"click"],["src","assets/icon/lanslide.jpg","alt","Landslide"],[1,"map-button-container"],["expand","block",1,"see-map-btn",3,"click"],[1,"notification-badge"]],template:function(r,n){r&1&&(i(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),c(3," ALERTO! "),o(),i(4,"ion-buttons",1)(5,"ion-button",2),d("click",function(){return n.openNotifications()}),s(6,"ion-icon",3),M(7,K,2,1,"ion-badge",4),o()()()(),i(8,"ion-content")(9,"div",5)(10,"div",6)(11,"div",7)(12,"h2",8),c(13,"Choose Emergency Type"),o(),i(14,"p",9),c(15,"Select a disaster type to view evacuation centers"),o()(),i(16,"div",10)(17,"ion-card",11),d("click",function(){return n.openDisasterMap("earthquake")}),i(18,"ion-card-content")(19,"div",12),s(20,"img",13),o(),i(21,"ion-text"),c(22,"Earthquake"),o(),s(23,"div",14),o()(),i(24,"ion-card",15),d("click",function(){return n.openDisasterMap("typhoon")}),i(25,"ion-card-content")(26,"div",12),s(27,"img",16),o(),i(28,"ion-text"),c(29,"Typhoon"),o(),s(30,"div",14),o()(),i(31,"ion-card",17),d("click",function(){return n.openDisasterMap("flashflood")}),i(32,"ion-card-content")(33,"div",12),s(34,"img",18),o(),i(35,"ion-text"),c(36,"Flash Flood"),o(),s(37,"div",14),o()(),i(38,"ion-card",19),d("click",function(){return n.openDisasterMap("fire")}),i(39,"ion-card-content")(40,"div",12),s(41,"img",20),o(),i(42,"ion-text"),c(43,"Fire"),o(),s(44,"div",14),o()(),i(45,"ion-card",21),d("click",function(){return n.openDisasterMap("landslide")}),i(46,"ion-card-content")(47,"div",12),s(48,"img",22),o(),i(49,"ion-text"),c(50,"Landslide"),o(),s(51,"div",14),o()()(),i(52,"div",23)(53,"ion-button",24),d("click",function(){return n.viewMap()}),c(54," See the whole map "),o()()()()()),r&2&&(u("translucent",!0),m(7),u("ngIf",n.unreadNotificationCount>0))},dependencies:[A,E,I,H,$,z,j,N,T,U,F,q,k,w],styles:['@charset "UTF-8";.status-text[_ngcontent-%COMP%]{margin-left:8px}.home-content[_ngcontent-%COMP%]{padding:20px 16px;height:calc(100vh - 120px);overflow:hidden;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f8fafc,#e2e8f0);position:relative}.home-content[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background-image:radial-gradient(circle at 25% 25%,rgba(59,130,246,.05) 0%,transparent 50%),radial-gradient(circle at 75% 75%,rgba(34,197,94,.05) 0%,transparent 50%);pointer-events:none;z-index:0}ion-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb);box-shadow:0 4px 12px #3b82f633}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--color: white}ion-title[_ngcontent-%COMP%]{text-align:center;font-family:Poppins,Arial,sans-serif;font-size:1.8rem;font-weight:800;letter-spacing:1.5px;color:#fff!important;text-shadow:0 2px 4px rgba(0,0,0,.2)}ion-title[_ngcontent-%COMP%]:after{content:"\\26a0\\fe0f";margin-left:8px;font-size:1.2rem}.disaster-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:20px;margin:0;height:100%;position:relative;z-index:1}.welcome-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:16px;padding:0 20px;position:relative;z-index:2}.welcome-title[_ngcontent-%COMP%]{font-size:1.4rem;font-weight:800;color:#1e293b;margin:0 0 8px;letter-spacing:.5px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.welcome-subtitle[_ngcontent-%COMP%]{font-size:.9rem;color:#64748b;margin:0;font-weight:500;opacity:.8}.disaster[_ngcontent-%COMP%]{margin:0;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);width:100%;height:130px;border-radius:16px;box-shadow:0 4px 12px #0000001a;overflow:hidden;position:relative}.disaster[_ngcontent-%COMP%]:hover{transform:translateY(-4px) scale(1.02);box-shadow:0 8px 25px #00000026}.disaster[_ngcontent-%COMP%]:active{transform:translateY(-2px) scale(1.01);transition:all .1s ease}.disaster[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;padding:16px 12px;height:100%;position:relative;z-index:2}.disaster[_ngcontent-%COMP%]   .card-icon-wrapper[_ngcontent-%COMP%]{position:relative;margin-bottom:8px}.disaster[_ngcontent-%COMP%]   .card-icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;box-shadow:0 4px 12px #00000026;transition:all .3s ease;border:2px solid rgba(255,255,255,.3)}.disaster[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:.95rem;font-weight:700;letter-spacing:.3px;text-shadow:0 1px 2px rgba(0,0,0,.1);margin-top:4px;position:relative;z-index:2}.disaster[_ngcontent-%COMP%]   .card-indicator[_ngcontent-%COMP%]{position:absolute;bottom:8px;right:8px;width:8px;height:8px;background:#fffc;border-radius:50%;box-shadow:0 2px 4px #0003;transition:all .3s ease}.disaster[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background:linear-gradient(135deg,#fff3,#ffffff0d);z-index:1;pointer-events:none}.disaster[_ngcontent-%COMP%]:hover   .card-icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.1) rotate(5deg);box-shadow:0 6px 16px #0003}.disaster[_ngcontent-%COMP%]:hover   .card-indicator[_ngcontent-%COMP%]{transform:scale(1.3);background:#fff}.earthquake[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff8c00,orange,#ffb84d);border:2px solid rgba(255,140,0,.3)}.earthquake[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.earthquake[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #ff8c004d}.typhoon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#22c55e,#16a34a,#15803d);border:2px solid rgba(34,197,94,.3)}.typhoon[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.typhoon[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #22c55e4d}.flood[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb,#1d4ed8);border:2px solid rgba(59,130,246,.3)}.flood[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.flood[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #3b82f64d}.fire[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ef4444,#dc2626,#b91c1c);border:2px solid rgba(239,68,68,.3)}.fire[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.fire[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #ef44444d}.landslide[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f59e0b,#d97706,#b45309);border:2px solid rgba(245,158,11,.3)}.landslide[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#fff!important}.landslide[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #f59e0b4d}.view-map[_ngcontent-%COMP%]{margin-top:24px;--background: #00bfff}.view-map[_ngcontent-%COMP%]:hover{--background: #0090cc}.view-map[disabled][_ngcontent-%COMP%]{--background: #999}.disaster-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;grid-template-rows:repeat(3,1fr);gap:16px;padding:0 20px;max-width:420px;margin:0 auto;width:100%;position:relative}.disaster-grid[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-12px 8px;background:#fff9;border-radius:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 8px 32px #0000001a;z-index:-1}.disaster.landslide[_ngcontent-%COMP%]{grid-column:1/3;max-width:200px;margin:0 auto}.map-button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:24px;padding:0 20px;position:relative;z-index:1}.see-map-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6366f1,#4f46e5,#4338ca);color:#fff;border-radius:30px;font-weight:700;font-size:1.1rem;height:56px;max-width:320px;text-transform:none;transition:all .3s cubic-bezier(.4,0,.2,1);cursor:pointer;box-shadow:0 6px 20px #6366f14d;border:2px solid rgba(255,255,255,.2);letter-spacing:.5px;position:relative;overflow:hidden}.see-map-btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s ease}.see-map-btn[_ngcontent-%COMP%]:hover{transform:translateY(-3px) scale(1.02);box-shadow:0 10px 30px #6366f166;background:linear-gradient(135deg,#7c3aed,#6366f1,#4f46e5)}.see-map-btn[_ngcontent-%COMP%]:hover:before{left:100%}.see-map-btn[_ngcontent-%COMP%]:active{transform:translateY(-1px) scale(1.01);box-shadow:0 6px 20px #6366f14d;transition:all .1s ease}.see-map-btn[disabled][_ngcontent-%COMP%]{background:linear-gradient(135deg,#9ca3af,#6b7280);color:#d1d5db;cursor:not-allowed;box-shadow:0 2px 8px #0000001a}.see-map-btn[disabled][_ngcontent-%COMP%]:hover{transform:none;box-shadow:0 2px 8px #0000001a}.see-map-btn[disabled][_ngcontent-%COMP%]:before{display:none}.see-map-btn[_ngcontent-%COMP%]:after{content:"\\1f5fa\\fe0f";margin-left:8px;font-size:1.2rem}.notifications-section[_ngcontent-%COMP%]{margin-top:20px;border-top:1px solid var(--ion-color-light);padding-top:10px}ion-item-divider[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-primary);font-weight:700;font-size:1.1rem;letter-spacing:.5px;margin-bottom:8px}.notification-button[_ngcontent-%COMP%]{position:relative;--color: white;--background: rgba(255, 255, 255, .1);--border-radius: 12px;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.notification-button[_ngcontent-%COMP%]:hover{--background: rgba(255, 255, 255, .2);transform:scale(1.05)}.notification-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.4rem;color:#fff;filter:drop-shadow(0 2px 4px rgba(0,0,0,.2))}.notification-badge[_ngcontent-%COMP%]{position:absolute;top:6px;right:6px;background:linear-gradient(135deg,#ef4444,#dc2626);color:#fff;font-size:10px;font-weight:700;min-width:18px;height:18px;border-radius:9px;display:flex;align-items:center;justify-content:center;z-index:10;box-shadow:0 2px 8px #ef444466;border:2px solid white;animation:_ngcontent-%COMP%_pulse-notification 2s infinite}@keyframes _ngcontent-%COMP%_pulse-notification{0%{transform:scale(1);box-shadow:0 2px 8px #ef444466}50%{transform:scale(1.1);box-shadow:0 4px 12px #ef444499}to{transform:scale(1);box-shadow:0 2px 8px #ef444466}}']})}}return a})();export{se as HomePage};
